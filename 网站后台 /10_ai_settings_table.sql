-- AI设置表
CREATE TABLE IF NOT EXISTS `ai_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，NULL表示全局设置',
  `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
  `setting_value` text COMMENT '设置值',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_setting` (`user_id`, `setting_key`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI客服设置表';

-- 插入默认设置
INSERT INTO `ai_settings` (`user_id`, `setting_key`, `setting_value`) VALUES
(NULL, 'enabled', '1'),
(NULL, 'model', 'deepseek-chat'),
(NULL, 'deep_thinking_enabled', '0'),
(NULL, 'system_prompt', '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'),
(NULL, 'api_keys', '[]'),
(NULL, 'api_key_status', '[]')
ON DUPLICATE KEY UPDATE 
  `setting_value` = VALUES(`setting_value`),
  `updated_at` = CURRENT_TIMESTAMP;

-- API访问日志表
CREATE TABLE IF NOT EXISTS `ai_api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `user_message` text COMMENT '用户消息',
  `ai_response` text COMMENT 'AI回复',
  `model_used` varchar(50) DEFAULT NULL COMMENT '使用的模型',
  `api_key_used` varchar(20) DEFAULT NULL COMMENT '使用的API密钥（脱敏）',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
  `success` tinyint(1) DEFAULT 1 COMMENT '是否成功',
  `error_message` text COMMENT '错误信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '请求IP',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API调用日志表';

-- API密钥管理表
CREATE TABLE IF NOT EXISTS `ai_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) DEFAULT NULL COMMENT '密钥名称',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥（加密存储）',
  `key_hash` varchar(64) NOT NULL COMMENT '密钥哈希值',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `error_count` int(11) DEFAULT 0 COMMENT '错误次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_key_hash` (`key_hash`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_last_used` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API密钥管理表';

-- 会话上下文表
CREATE TABLE IF NOT EXISTS `ai_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL COMMENT '会话ID',
  `message_index` int(11) NOT NULL COMMENT '消息索引',
  `role` enum('user','assistant','system') NOT NULL COMMENT '角色',
  `content` text NOT NULL COMMENT '消息内容',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_session_message` (`session_id`, `message_index`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI会话上下文表';

-- 创建清理过期会话的存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanupOldConversations`()
BEGIN
    -- 删除7天前的会话记录
    DELETE FROM `ai_conversations` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- 删除30天前的API日志
    DELETE FROM `ai_api_logs` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
END //
DELIMITER ;

-- 创建定时清理事件（可选）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `cleanup_ai_data`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL CleanupOldConversations();
