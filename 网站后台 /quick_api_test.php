<?php
/**
 * DeepSeek API快速测试脚本
 * 简单快速地测试API接口是否正常工作
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 测试结果数组
$test_results = [
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => [],
    'summary' => [
        'total' => 0,
        'passed' => 0,
        'failed' => 0
    ]
];

// 测试函数
function addTestResult($name, $status, $message, $details = null) {
    global $test_results;
    
    $test_results['tests'][] = [
        'name' => $name,
        'status' => $status,
        'message' => $message,
        'details' => $details
    ];
    
    $test_results['summary']['total']++;
    if ($status === 'success') {
        $test_results['summary']['passed']++;
    } else {
        $test_results['summary']['failed']++;
    }
}

// 1. 测试数据库连接
try {
    require_once 'includes/db.php';
    if ($pdo) {
        addTestResult('数据库连接', 'success', '数据库连接成功');
        
        // 检查AI相关表
        $ai_tables = ['ai_settings', 'ai_api_logs', 'ai_api_keys', 'ai_conversations'];
        $existing_tables = [];
        
        foreach ($ai_tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $existing_tables[] = $table;
                }
            } catch (Exception $e) {
                // 忽略单个表的错误
            }
        }
        
        if (count($existing_tables) === count($ai_tables)) {
            addTestResult('数据库表结构', 'success', '所有AI相关表都存在', $existing_tables);
        } else {
            addTestResult('数据库表结构', 'warning', '部分表缺失', [
                'existing' => $existing_tables,
                'missing' => array_diff($ai_tables, $existing_tables)
            ]);
        }
    } else {
        addTestResult('数据库连接', 'error', '数据库连接失败');
    }
} catch (Exception $e) {
    addTestResult('数据库连接', 'error', '数据库连接异常: ' . $e->getMessage());
}

// 2. 测试API文件存在性
$api_files = [
    'api/deepseek_api.php' => 'DeepSeek API主文件',
    'api/deepseek_config.php' => '配置管理文件',
    'api/api_base.php' => 'API基础类',
    'xuxuemei/templates/ai_service_settings.php' => 'AI设置界面'
];

$missing_files = [];
foreach ($api_files as $file => $description) {
    if (!file_exists($file)) {
        $missing_files[] = $file;
    }
}

if (empty($missing_files)) {
    addTestResult('API文件完整性', 'success', '所有必需文件都存在');
} else {
    addTestResult('API文件完整性', 'error', '部分文件缺失', $missing_files);
}

// 3. 测试配置文件加载
try {
    if (file_exists('api/deepseek_config.php')) {
        require_once 'api/deepseek_config.php';
        $config = getDeepSeekConfig();
        
        $config_info = [
            'api_timeout' => $config->get('api_timeout'),
            'max_tokens' => $config->get('max_tokens'),
            'supported_models' => count($config->get('supported_models'))
        ];
        
        addTestResult('配置文件加载', 'success', '配置文件加载成功', $config_info);
    } else {
        addTestResult('配置文件加载', 'error', '配置文件不存在');
    }
} catch (Exception $e) {
    addTestResult('配置文件加载', 'error', '配置文件加载失败: ' . $e->getMessage());
}

// 4. 测试API类实例化
try {
    if (file_exists('api/deepseek_api.php')) {
        // 模拟API请求环境
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['PATH_INFO'] = '/status';
        $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer test-key';
        
        // 捕获输出
        ob_start();
        
        // 尝试包含API文件（但不执行）
        $api_content = file_get_contents('api/deepseek_api.php');
        if (strpos($api_content, 'class DeepSeekAPI') !== false) {
            addTestResult('API类定义', 'success', 'DeepSeek API类定义正确');
        } else {
            addTestResult('API类定义', 'error', 'API类定义不完整');
        }
        
        ob_end_clean();
    } else {
        addTestResult('API类定义', 'error', 'API文件不存在');
    }
} catch (Exception $e) {
    addTestResult('API类定义', 'error', 'API类检查失败: ' . $e->getMessage());
}

// 5. 测试前端界面文件
try {
    if (file_exists('xuxuemei/templates/ai_service_settings.php')) {
        $template_content = file_get_contents('xuxuemei/templates/ai_service_settings.php');
        
        // 检查关键元素
        $key_elements = [
            'aiSettings' => strpos($template_content, 'aiSettings') !== false,
            'deepseek' => strpos($template_content, 'deepseek') !== false || strpos($template_content, 'DeepSeek') !== false,
            'api_key' => strpos($template_content, 'apiKey') !== false || strpos($template_content, 'api_key') !== false
        ];
        
        $valid_elements = array_filter($key_elements);
        
        if (count($valid_elements) >= 2) {
            addTestResult('前端界面检查', 'success', '前端界面包含必要元素', $key_elements);
        } else {
            addTestResult('前端界面检查', 'warning', '前端界面可能不完整', $key_elements);
        }
    } else {
        addTestResult('前端界面检查', 'error', '前端界面文件不存在');
    }
} catch (Exception $e) {
    addTestResult('前端界面检查', 'error', '前端界面检查失败: ' . $e->getMessage());
}

// 6. 生成测试建议
$suggestions = [];

if ($test_results['summary']['failed'] > 0) {
    $suggestions[] = "发现 {$test_results['summary']['failed']} 个问题需要修复";
    
    foreach ($test_results['tests'] as $test) {
        if ($test['status'] === 'error') {
            switch ($test['name']) {
                case '数据库连接':
                    $suggestions[] = "请检查 includes/db.php 中的数据库配置";
                    break;
                case '数据库表结构':
                    $suggestions[] = "请运行 fix_ai_database.php 修复数据库表";
                    break;
                case 'API文件完整性':
                    $suggestions[] = "请确保所有API文件都已正确上传";
                    break;
                case '配置文件加载':
                    $suggestions[] = "请检查 api/deepseek_config.php 文件";
                    break;
            }
        }
    }
}

if ($test_results['summary']['passed'] === $test_results['summary']['total']) {
    $suggestions[] = "🎉 所有测试都通过了！您可以开始使用DeepSeek API功能";
    $suggestions[] = "访问 xuxuemei/index.php?page=ai_service_settings 配置AI设置";
    $suggestions[] = "使用 api/test_deepseek_api.html 测试API接口";
}

$test_results['suggestions'] = $suggestions;
$test_results['status'] = $test_results['summary']['failed'] === 0 ? 'success' : 'error';

// 输出结果
echo json_encode($test_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
