<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API状态测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .test-result { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-refresh { background: #17a2b8; }
        .btn-refresh:hover { background: #117a8b; }
        .loading { text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .summary { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .test-item { background: white; border-radius: 8px; padding: 15px; margin: 10px 0; border: 1px solid #e9ecef; }
        .test-name { font-weight: bold; margin-bottom: 5px; }
        .test-message { margin-bottom: 10px; }
        .test-details { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
        .suggestions { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0; }
        .suggestions ul { padding-left: 20px; }
        .suggestions li { margin: 5px 0; }
        .quick-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .quick-link { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; text-align: center; }
        .quick-link a { text-decoration: none; color: #007bff; font-weight: bold; }
        .quick-link a:hover { color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 DeepSeek API状态测试</h1>
        
        <div class="test-result">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在测试API状态...</p>
            </div>
            
            <div id="test-results" style="display: none;">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <div class="quick-links">
            <div class="quick-link">
                <h4>🔧 数据库修复</h4>
                <a href="fix_ai_database.php" target="_blank">修复数据库表</a>
            </div>
            <div class="quick-link">
                <h4>⚙️ AI设置</h4>
                <a href="xuxuemei/index.php?page=ai_service_settings" target="_blank">AI客服设置</a>
            </div>
            <div class="quick-link">
                <h4>🧪 API测试</h4>
                <a href="api/test_deepseek_api.html" target="_blank">API测试工具</a>
            </div>
            <div class="quick-link">
                <h4>📚 API文档</h4>
                <a href="api/README.md" target="_blank">查看文档</a>
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runTest()" class="btn btn-refresh">🔄 重新测试</button>
            <a href="test_deepseek_integration.php" class="btn">📋 详细测试</a>
        </div>
    </div>

    <script>
        // 运行测试
        async function runTest() {
            const loading = document.getElementById('loading');
            const results = document.getElementById('test-results');
            
            loading.style.display = 'block';
            results.style.display = 'none';
            
            try {
                const response = await fetch('quick_api_test.php');
                const data = await response.json();
                
                displayResults(data);
                
                loading.style.display = 'none';
                results.style.display = 'block';
            } catch (error) {
                loading.style.display = 'none';
                results.innerHTML = `
                    <div class="status error">
                        ❌ 测试失败: ${error.message}
                    </div>
                `;
                results.style.display = 'block';
            }
        }
        
        // 显示测试结果
        function displayResults(data) {
            const results = document.getElementById('test-results');
            
            let html = `
                <div class="summary">
                    <h3>📊 测试总结</h3>
                    <p><strong>测试时间:</strong> ${data.timestamp}</p>
                    <p><strong>总测试项:</strong> ${data.summary.total}</p>
                    <p><strong>通过:</strong> <span style="color: #28a745;">${data.summary.passed}</span></p>
                    <p><strong>失败:</strong> <span style="color: #dc3545;">${data.summary.failed}</span></p>
                    <p><strong>整体状态:</strong> 
                        <span class="${data.status === 'success' ? 'success' : 'error'}" style="padding: 5px 10px; border-radius: 3px;">
                            ${data.status === 'success' ? '✅ 正常' : '❌ 异常'}
                        </span>
                    </p>
                </div>
                
                <h3>🔍 详细测试结果</h3>
            `;
            
            // 显示每个测试项
            data.tests.forEach(test => {
                let statusClass = test.status;
                let statusIcon = test.status === 'success' ? '✅' : (test.status === 'warning' ? '⚠️' : '❌');
                
                html += `
                    <div class="test-item">
                        <div class="test-name">${statusIcon} ${test.name}</div>
                        <div class="test-message">${test.message}</div>
                `;
                
                if (test.details) {
                    html += `<div class="test-details">${JSON.stringify(test.details, null, 2)}</div>`;
                }
                
                html += `</div>`;
            });
            
            // 显示建议
            if (data.suggestions && data.suggestions.length > 0) {
                html += `
                    <div class="suggestions">
                        <h3>💡 建议和下一步操作</h3>
                        <ul>
                `;
                
                data.suggestions.forEach(suggestion => {
                    html += `<li>${suggestion}</li>`;
                });
                
                html += `
                        </ul>
                    </div>
                `;
            }
            
            results.innerHTML = html;
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            runTest();
        };
    </script>
</body>
</html>
