-- AI基础表结构 - 最简化版本
-- 避免复杂语法，确保导入成功

-- AI设置表
CREATE TABLE IF NOT EXISTS `ai_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- AI日志表
CREATE TABLE IF NOT EXISTS `ai_api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) DEFAULT NULL,
  `user_message` text,
  `ai_response` text,
  `model_used` varchar(50) DEFAULT NULL,
  `api_key_used` varchar(20) DEFAULT NULL,
  `response_time` int(11) DEFAULT NULL,
  `tokens_used` int(11) DEFAULT NULL,
  `success` tinyint(1) DEFAULT 1,
  `error_message` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- API密钥表
CREATE TABLE IF NOT EXISTS `ai_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) DEFAULT NULL,
  `api_key` varchar(255) NOT NULL,
  `key_hash` varchar(64) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  `error_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 会话表
CREATE TABLE IF NOT EXISTS `ai_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL,
  `message_index` int(11) NOT NULL,
  `role` enum('user','assistant','system') NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入基础设置数据
INSERT IGNORE INTO `ai_settings` (`user_id`, `setting_key`, `setting_value`) VALUES
(NULL, 'enabled', '1'),
(NULL, 'model', 'deepseek-chat'),
(NULL, 'deep_thinking_enabled', '0'),
(NULL, 'api_keys', '[]'),
(NULL, 'api_key_status', '[]');
