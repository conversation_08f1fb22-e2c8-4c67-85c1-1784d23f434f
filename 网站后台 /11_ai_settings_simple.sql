-- AI设置表 - 简化版本，避免导入问题
-- 分步骤创建，确保兼容性

-- 第一步：创建AI设置表
CREATE TABLE IF NOT EXISTS `ai_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 第二步：添加唯一约束（如果不存在）
ALTER TABLE `ai_settings` ADD UNIQUE KEY `unique_user_setting` (`user_id`, `setting_key`);

-- 第三步：插入默认设置
INSERT IGNORE INTO `ai_settings` (`user_id`, `setting_key`, `setting_value`) VALUES
(NULL, 'enabled', '1'),
(NULL, 'model', 'deepseek-chat'),
(NULL, 'deep_thinking_enabled', '0'),
(NULL, 'system_prompt', '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'),
(NULL, 'api_keys', '[]'),
(NULL, 'api_key_status', '[]');

-- 第四步：创建API访问日志表
CREATE TABLE IF NOT EXISTS `ai_api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) DEFAULT NULL,
  `user_message` text,
  `ai_response` text,
  `model_used` varchar(50) DEFAULT NULL,
  `api_key_used` varchar(20) DEFAULT NULL,
  `response_time` int(11) DEFAULT NULL,
  `tokens_used` int(11) DEFAULT NULL,
  `success` tinyint(1) DEFAULT 1,
  `error_message` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 第五步：创建API密钥管理表
CREATE TABLE IF NOT EXISTS `ai_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) DEFAULT NULL,
  `api_key` varchar(255) NOT NULL,
  `key_hash` varchar(64) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  `error_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_last_used` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 第六步：添加API密钥表的唯一约束
ALTER TABLE `ai_api_keys` ADD UNIQUE KEY `unique_key_hash` (`key_hash`);

-- 第七步：创建会话上下文表
CREATE TABLE IF NOT EXISTS `ai_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL,
  `message_index` int(11) NOT NULL,
  `role` enum('user','assistant','system') NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 第八步：添加会话表的唯一约束
ALTER TABLE `ai_conversations` ADD UNIQUE KEY `unique_session_message` (`session_id`, `message_index`);
