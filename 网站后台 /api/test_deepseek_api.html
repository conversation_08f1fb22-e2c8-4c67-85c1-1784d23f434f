<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API 测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .test-section h3::before {
            content: "🔧";
            margin-right: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .response {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .response.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .response.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .chat-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .chat-messages {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        
        .message.user {
            background: #e3f2fd;
            margin-left: 20px;
        }
        
        .message.assistant {
            background: #f3e5f5;
            margin-right: 20px;
        }
        
        .message .role {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .message.user .role {
            color: #1976d2;
        }
        
        .message.assistant .role {
            color: #7b1fa2;
        }
        
        @media (max-width: 768px) {
            .chat-container {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 DeepSeek API 测试工具</h1>
            <p>测试网站后台DeepSeek AI客服API接口功能</p>
        </div>
        
        <!-- API配置 -->
        <div class="test-section">
            <h3>API配置</h3>
            <div class="form-group">
                <label for="apiKey">API密钥:</label>
                <input type="password" id="apiKey" placeholder="输入您的API密钥">
            </div>
            <div class="form-group">
                <label for="baseUrl">API基础URL:</label>
                <input type="text" id="baseUrl" value="deepseek_api.php" placeholder="API基础URL">
            </div>
        </div>
        
        <!-- 聊天测试 -->
        <div class="test-section">
            <h3>聊天测试</h3>
            <div class="chat-container">
                <div>
                    <div class="form-group">
                        <label for="chatMessage">消息:</label>
                        <textarea id="chatMessage" placeholder="输入要发送的消息"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="sessionId">会话ID:</label>
                        <input type="text" id="sessionId" placeholder="可选，留空自动生成">
                    </div>
                    <div class="form-group">
                        <label for="customPrompt">自定义提示词:</label>
                        <textarea id="customPrompt" placeholder="可选，自定义系统提示词"></textarea>
                    </div>
                    <button class="btn" onclick="sendChatMessage()">发送消息</button>
                    <button class="btn" onclick="clearChat()">清空对话</button>
                </div>
                <div>
                    <label>对话历史:</label>
                    <div id="chatMessages" class="chat-messages"></div>
                </div>
            </div>
            <div id="chatResponse" class="response" style="display: none;"></div>
        </div>
        
        <!-- 状态查询 -->
        <div class="test-section">
            <h3>状态查询</h3>
            <button class="btn" onclick="getStatus()">获取AI状态</button>
            <div id="statusResponse" class="response" style="display: none;"></div>
        </div>
        
        <!-- 模型列表 -->
        <div class="test-section">
            <h3>模型列表</h3>
            <button class="btn" onclick="getModels()">获取模型列表</button>
            <div id="modelsResponse" class="response" style="display: none;"></div>
        </div>
        
        <!-- 设置管理 -->
        <div class="test-section">
            <h3>设置管理</h3>
            <button class="btn" onclick="getSettings()">获取设置</button>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <div id="settingsResponse" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        let currentSessionId = '';
        
        // 发送聊天消息
        async function sendChatMessage() {
            const apiKey = document.getElementById('apiKey').value;
            const baseUrl = document.getElementById('baseUrl').value;
            const message = document.getElementById('chatMessage').value;
            const sessionId = document.getElementById('sessionId').value || currentSessionId;
            const customPrompt = document.getElementById('customPrompt').value;
            
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }
            
            if (!message) {
                alert('请输入消息内容');
                return;
            }
            
            const responseDiv = document.getElementById('chatResponse');
            responseDiv.style.display = 'block';
            responseDiv.className = 'response';
            responseDiv.textContent = '发送中...';
            
            try {
                const response = await fetch(`${baseUrl}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        custom_prompt: customPrompt
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    responseDiv.className = 'response success';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                    
                    // 更新会话ID
                    currentSessionId = result.data.session_id;
                    document.getElementById('sessionId').value = currentSessionId;
                    
                    // 添加到对话历史
                    addMessageToChat('user', message);
                    addMessageToChat('assistant', result.data.message);
                    
                    // 清空输入框
                    document.getElementById('chatMessage').value = '';
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.textContent = '请求失败: ' + error.message;
            }
        }
        
        // 添加消息到对话历史
        function addMessageToChat(role, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.innerHTML = `
                <div class="role">${role === 'user' ? '用户' : 'AI助手'}</div>
                <div>${content}</div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 清空对话
        function clearChat() {
            document.getElementById('chatMessages').innerHTML = '';
            currentSessionId = '';
            document.getElementById('sessionId').value = '';
        }
        
        // 获取状态
        async function getStatus() {
            await makeApiRequest('status', 'GET', null, 'statusResponse');
        }
        
        // 获取模型列表
        async function getModels() {
            await makeApiRequest('models', 'GET', null, 'modelsResponse');
        }
        
        // 获取设置
        async function getSettings() {
            await makeApiRequest('settings/get', 'GET', null, 'settingsResponse');
        }
        
        // 测试连接
        async function testConnection() {
            const apiKey = document.getElementById('apiKey').value;
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }
            
            await makeApiRequest('settings/test', 'POST', {
                api_key: 'sk-test-key-for-validation'
            }, 'settingsResponse');
        }
        
        // 通用API请求函数
        async function makeApiRequest(endpoint, method, data, responseElementId) {
            const apiKey = document.getElementById('apiKey').value;
            const baseUrl = document.getElementById('baseUrl').value;
            
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }
            
            const responseDiv = document.getElementById(responseElementId);
            responseDiv.style.display = 'block';
            responseDiv.className = 'response';
            responseDiv.textContent = '请求中...';
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    }
                };
                
                if (data && method !== 'GET') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${baseUrl}/${endpoint}`, options);
                const result = await response.json();
                
                if (result.success) {
                    responseDiv.className = 'response success';
                } else {
                    responseDiv.className = 'response error';
                }
                
                responseDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.textContent = '请求失败: ' + error.message;
            }
        }
        
        // 页面加载时生成随机会话ID
        window.onload = function() {
            currentSessionId = 'test_' + Date.now();
            document.getElementById('sessionId').value = currentSessionId;
        };
    </script>
</body>
</html>
