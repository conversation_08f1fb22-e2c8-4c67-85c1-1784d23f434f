<?php
/**
 * DeepSeek API使用示例
 * 
 * 演示如何使用DeepSeek API接口
 */

// 配置信息
$apiKey = 'your-api-key-here'; // 替换为您的API密钥
$baseUrl = 'http://localhost/api/deepseek_api.php'; // 替换为实际的API地址

/**
 * 发送HTTP请求
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    $defaultHeaders = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $GLOBALS['apiKey']
    ];
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => array_merge($defaultHeaders, $headers),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception('CURL error: ' . $error);
    }
    
    $result = json_decode($response, true);
    if ($httpCode !== 200) {
        throw new Exception('HTTP error ' . $httpCode . ': ' . ($result['error'] ?? 'Unknown error'));
    }
    
    return $result;
}

/**
 * 示例1: 发送聊天消息
 */
function exampleChat() {
    global $baseUrl;
    
    echo "=== 聊天示例 ===\n";
    
    try {
        $response = makeRequest($baseUrl . '/chat', 'POST', [
            'message' => '你好，我想了解一下你们的产品',
            'session_id' => 'example_session_' . time()
        ]);
        
        if ($response['success']) {
            echo "AI回复: " . $response['data']['message'] . "\n";
            echo "会话ID: " . $response['data']['session_id'] . "\n";
            echo "使用模型: " . $response['data']['model'] . "\n";
            
            if (isset($response['data']['usage'])) {
                echo "Token使用: " . $response['data']['usage']['total_tokens'] . "\n";
            }
        } else {
            echo "错误: " . $response['error'] . "\n";
        }
    } catch (Exception $e) {
        echo "异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

/**
 * 示例2: 获取AI状态
 */
function exampleStatus() {
    global $baseUrl;
    
    echo "=== 状态查询示例 ===\n";
    
    try {
        $response = makeRequest($baseUrl . '/status');
        
        if ($response['success']) {
            $data = $response['data'];
            echo "AI状态: " . ($data['status'] === 'active' ? '活跃' : '非活跃') . "\n";
            echo "是否启用: " . ($data['enabled'] ? '是' : '否') . "\n";
            echo "API密钥总数: " . $data['total_api_keys'] . "\n";
            echo "可用API密钥: " . $data['available_api_keys'] . "\n";
            echo "当前模型: " . $data['current_model'] . "\n";
            echo "深度思考: " . ($data['deep_thinking_enabled'] ? '启用' : '禁用') . "\n";
        } else {
            echo "错误: " . $response['error'] . "\n";
        }
    } catch (Exception $e) {
        echo "异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

/**
 * 示例3: 获取模型列表
 */
function exampleModels() {
    global $baseUrl;
    
    echo "=== 模型列表示例 ===\n";
    
    try {
        $response = makeRequest($baseUrl . '/models');
        
        if ($response['success']) {
            echo "当前模型: " . $response['data']['current_model'] . "\n";
            echo "支持的模型:\n";
            
            foreach ($response['data']['models'] as $model) {
                echo "- " . $model['id'] . ": " . $model['name'] . "\n";
                echo "  描述: " . $model['description'] . "\n";
                echo "  最大Token: " . $model['max_tokens'] . "\n";
                echo "  支持深度思考: " . ($model['supports_deep_thinking'] ? '是' : '否') . "\n";
                echo "\n";
            }
        } else {
            echo "错误: " . $response['error'] . "\n";
        }
    } catch (Exception $e) {
        echo "异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

/**
 * 示例4: 获取设置
 */
function exampleGetSettings() {
    global $baseUrl;
    
    echo "=== 获取设置示例 ===\n";
    
    try {
        $response = makeRequest($baseUrl . '/settings/get');
        
        if ($response['success']) {
            $data = $response['data'];
            echo "AI启用状态: " . ($data['enabled'] ? '启用' : '禁用') . "\n";
            echo "当前模型: " . $data['model'] . "\n";
            echo "深度思考: " . ($data['deepThinkingEnabled'] ? '启用' : '禁用') . "\n";
            echo "API密钥数量: " . count($data['apiKeys']) . "\n";
            echo "系统提示词: " . substr($data['systemPrompt'], 0, 50) . "...\n";
        } else {
            echo "错误: " . $response['error'] . "\n";
        }
    } catch (Exception $e) {
        echo "异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

/**
 * 示例5: 连续对话
 */
function exampleConversation() {
    global $baseUrl;
    
    echo "=== 连续对话示例 ===\n";
    
    $sessionId = 'conversation_' . time();
    $messages = [
        '你好，我想了解一下你们的产品',
        '价格怎么样？',
        '有什么优惠活动吗？'
    ];
    
    foreach ($messages as $index => $message) {
        echo "用户: " . $message . "\n";
        
        try {
            $response = makeRequest($baseUrl . '/chat', 'POST', [
                'message' => $message,
                'session_id' => $sessionId
            ]);
            
            if ($response['success']) {
                echo "AI: " . $response['data']['message'] . "\n";
            } else {
                echo "错误: " . $response['error'] . "\n";
            }
        } catch (Exception $e) {
            echo "异常: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        
        // 添加延迟以避免请求过于频繁
        if ($index < count($messages) - 1) {
            sleep(1);
        }
    }
}

/**
 * 示例6: 自定义提示词
 */
function exampleCustomPrompt() {
    global $baseUrl;
    
    echo "=== 自定义提示词示例 ===\n";
    
    try {
        $response = makeRequest($baseUrl . '/chat', 'POST', [
            'message' => '介绍一下你们的服务',
            'custom_prompt' => '你是一名专业的技术支持工程师，请用专业但易懂的语言回答用户问题。',
            'session_id' => 'custom_prompt_' . time()
        ]);
        
        if ($response['success']) {
            echo "用户: 介绍一下你们的服务\n";
            echo "AI (技术支持): " . $response['data']['message'] . "\n";
        } else {
            echo "错误: " . $response['error'] . "\n";
        }
    } catch (Exception $e) {
        echo "异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 运行示例
if (php_sapi_name() === 'cli') {
    echo "DeepSeek API 使用示例\n";
    echo "=====================\n\n";
    
    // 检查API密钥
    if ($apiKey === 'your-api-key-here') {
        echo "请先在文件顶部设置您的API密钥！\n";
        exit(1);
    }
    
    // 运行各种示例
    exampleStatus();
    exampleModels();
    exampleGetSettings();
    exampleChat();
    exampleConversation();
    exampleCustomPrompt();
    
    echo "所有示例执行完成！\n";
} else {
    echo "请在命令行中运行此脚本: php example_usage.php\n";
}
