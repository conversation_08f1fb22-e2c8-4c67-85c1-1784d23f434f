<?php
/**
 * DeepSeek AI客服API接口 - 简化版本
 * 
 * 提供对网站后台DeepSeek AI功能的API访问
 * 不依赖复杂的基础类，直接实现功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * 简化的DeepSeek API处理类
 */
class SimpleDeepSeekAPI {
    
    private $db;
    private $method;
    private $request;
    private $aiSettings;
    
    public function __construct() {
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->request = $this->getRequestData();
        $this->connectDatabase();
        $this->loadAISettings();
    }
    
    /**
     * 连接数据库
     */
    private function connectDatabase() {
        try {
            // 使用现有的数据库连接
            require_once '../includes/db.php';
            global $pdo;
            $this->db = $pdo;
            
            if (!$this->db) {
                throw new Exception('Database connection failed');
            }
        } catch (Exception $e) {
            $this->respondError('Database connection failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取请求数据
     */
    private function getRequestData() {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?: [];
    }
    
    /**
     * 加载AI设置
     */
    private function loadAISettings() {
        $this->aiSettings = [
            'enabled' => true,
            'apiKeys' => [],
            'apiKeyStatus' => [],
            'currentApiKeyIndex' => 0,
            'model' => 'deepseek-chat',
            'deepThinkingEnabled' => false,
            'replyDelay' => 0,
            'systemPrompt' => '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
        ];
        
        // 从数据库加载设置
        if ($this->db) {
            try {
                $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM ai_settings WHERE user_id IS NULL");
                $stmt->execute();
                
                while ($row = $stmt->fetch()) {
                    switch ($row['setting_key']) {
                        case 'enabled':
                            $this->aiSettings['enabled'] = (bool)$row['setting_value'];
                            break;
                        case 'api_keys':
                            $this->aiSettings['apiKeys'] = json_decode($row['setting_value'], true) ?: [];
                            break;
                        case 'api_key_status':
                            $this->aiSettings['apiKeyStatus'] = json_decode($row['setting_value'], true) ?: [];
                            break;
                        case 'model':
                            $this->aiSettings['model'] = $row['setting_value'];
                            break;
                        case 'deep_thinking_enabled':
                            $this->aiSettings['deepThinkingEnabled'] = (bool)$row['setting_value'];
                            break;
                        case 'system_prompt':
                            $this->aiSettings['systemPrompt'] = $row['setting_value'];
                            break;
                    }
                }
            } catch (Exception $e) {
                // 使用默认设置
            }
        }
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        // 解析请求路径
        $path = $_SERVER['PATH_INFO'] ?? '';
        $path = trim($path, '/');
        
        // 分割路径获取端点和操作
        $segments = explode('/', $path);
        $endpoint = $segments[0] ?? '';
        $action = $segments[1] ?? '';
        
        // 验证API密钥（简化版本）
        if (!$this->validateApiKey()) {
            return $this->respondError('Invalid API key', 401);
        }
        
        // 路由处理
        switch ($endpoint) {
            case 'status':
                return $this->handleStatus();
            case 'models':
                return $this->handleModels();
            case 'settings':
                return $this->handleSettings($action);
            case 'chat':
                return $this->handleChat();
            default:
                return $this->respondError('Invalid endpoint', 404);
        }
    }
    
    /**
     * 验证API密钥（简化版本）
     */
    private function validateApiKey() {
        $apiKey = $this->getRequestApiKey();
        
        // 简化验证：检查是否有Authorization头或X-API-Key头
        return !empty($apiKey) && strlen($apiKey) >= 10;
    }
    
    /**
     * 获取请求中的API密钥
     */
    private function getRequestApiKey() {
        // 从Authorization头获取
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        // 从X-API-Key头获取
        return $_SERVER['HTTP_X_API_KEY'] ?? '';
    }
    
    /**
     * 处理状态查询
     */
    private function handleStatus() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }
        
        $availableKeys = count(array_filter($this->aiSettings['apiKeyStatus']));
        
        return $this->respondSuccess([
            'enabled' => $this->aiSettings['enabled'],
            'total_api_keys' => count($this->aiSettings['apiKeys']),
            'available_api_keys' => $availableKeys,
            'current_model' => $this->aiSettings['model'],
            'deep_thinking_enabled' => $this->aiSettings['deepThinkingEnabled'],
            'status' => $this->aiSettings['enabled'] && $availableKeys > 0 ? 'active' : 'inactive',
            'timestamp' => time()
        ]);
    }
    
    /**
     * 处理模型列表查询
     */
    private function handleModels() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }
        
        return $this->respondSuccess([
            'models' => [
                [
                    'id' => 'deepseek-chat',
                    'name' => 'DeepSeek Chat',
                    'description' => '通用对话模型，适合日常客服场景',
                    'max_tokens' => 4000,
                    'supports_deep_thinking' => false
                ],
                [
                    'id' => 'deepseek-reasoner',
                    'name' => 'DeepSeek-R1-0528',
                    'description' => '推理模型，支持深度思考功能',
                    'max_tokens' => 8000,
                    'supports_deep_thinking' => true
                ]
            ],
            'current_model' => $this->aiSettings['model']
        ]);
    }
    
    /**
     * 处理设置相关请求
     */
    private function handleSettings($action) {
        switch ($action) {
            case 'get':
                return $this->getSettings();
            default:
                return $this->respondError('Invalid settings action', 400);
        }
    }
    
    /**
     * 获取AI设置
     */
    private function getSettings() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 隐藏敏感信息
        $settings = $this->aiSettings;
        $settings['apiKeys'] = array_map(function($key) {
            return substr($key, 0, 8) . '...' . substr($key, -8);
        }, $settings['apiKeys']);
        
        return $this->respondSuccess($settings);
    }
    
    /**
     * 处理聊天请求
     */
    private function handleChat() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        $message = $this->request['message'] ?? '';
        if (empty($message)) {
            return $this->respondError('Message is required', 400);
        }
        
        // 简化的聊天响应（演示用）
        return $this->respondSuccess([
            'message' => '这是一个演示回复：' . $message,
            'session_id' => $this->request['session_id'] ?? uniqid(),
            'model' => $this->aiSettings['model'],
            'timestamp' => time()
        ]);
    }
    
    /**
     * 成功响应
     */
    private function respondSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 错误响应
     */
    private function respondError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'code' => $code
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 创建API实例并处理请求
try {
    $api = new SimpleDeepSeekAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
