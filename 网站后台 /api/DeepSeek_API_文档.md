# DeepSeek AI客服API接口文档

## 概述

本API提供对网站后台DeepSeek AI客服功能的完整访问，支持聊天对话、设置管理、状态查询等功能。

## 基础信息

- **基础URL**: `https://your-domain.com/api/deepseek_api.php`
- **认证方式**: API密钥认证
- **请求格式**: JSON
- **响应格式**: JSON

## 认证

所有API请求都需要包含有效的API密钥，可以通过以下两种方式提供：

### 方式1: Authorization头
```
Authorization: Bearer YOUR_API_KEY
```

### 方式2: X-API-Key头
```
X-API-Key: YOUR_API_KEY
```

## API端点

### 1. 聊天对话

**端点**: `POST /chat`

发送消息给AI并获取回复。

#### 请求参数

```json
{
  "message": "用户消息内容",
  "session_id": "会话ID（可选，不提供会自动生成）",
  "custom_prompt": "自定义系统提示词（可选）",
  "model": "模型名称（可选，默认使用配置的模型）",
  "deep_thinking": true/false
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "message": "AI回复内容",
    "session_id": "会话ID",
    "model": "deepseek-chat",
    "usage": {
      "prompt_tokens": 50,
      "completion_tokens": 30,
      "total_tokens": 80
    },
    "timestamp": 1642678800
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "error": "错误信息",
  "code": 400
}
```

### 2. 获取设置

**端点**: `GET /settings/get`

获取当前AI设置配置。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "apiKeys": ["sk-1234****", "sk-5678****"],
    "model": "deepseek-chat",
    "deepThinkingEnabled": false,
    "systemPrompt": "你是一名专业的客服人员..."
  }
}
```

### 3. 更新设置

**端点**: `POST /settings/update`

更新AI设置配置。

#### 请求参数

```json
{
  "enabled": true,
  "model": "deepseek-chat",
  "deep_thinking_enabled": false,
  "system_prompt": "自定义系统提示词",
  "api_keys": ["sk-your-api-key-1", "sk-your-api-key-2"]
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "message": "Settings updated successfully"
  }
}
```

### 4. 测试API连接

**端点**: `POST /settings/test`

测试DeepSeek API密钥的有效性。

#### 请求参数

```json
{
  "api_key": "sk-your-deepseek-api-key"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "valid": true,
    "models": [
      {
        "id": "deepseek-chat",
        "object": "model"
      }
    ],
    "message": "API密钥验证成功"
  }
}
```

### 5. 获取状态

**端点**: `GET /status`

获取AI服务当前状态。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "total_api_keys": 2,
    "available_api_keys": 1,
    "current_model": "deepseek-chat",
    "deep_thinking_enabled": false,
    "status": "active"
  }
}
```

### 6. 获取模型列表

**端点**: `GET /models`

获取支持的AI模型列表。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "deepseek-chat",
        "name": "DeepSeek Chat",
        "description": "通用对话模型，适合日常客服场景"
      },
      {
        "id": "deepseek-reasoner",
        "name": "DeepSeek-R1-0528",
        "description": "推理模型，支持深度思考功能"
      }
    ],
    "current_model": "deepseek-chat"
  }
}
```

## 使用示例

### JavaScript示例

```javascript
// 发送聊天消息
async function sendMessage(message, sessionId = null) {
    const response = await fetch('/api/deepseek_api.php/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer YOUR_API_KEY'
        },
        body: JSON.stringify({
            message: message,
            session_id: sessionId
        })
    });
    
    const result = await response.json();
    return result;
}

// 获取AI状态
async function getAIStatus() {
    const response = await fetch('/api/deepseek_api.php/status', {
        headers: {
            'Authorization': 'Bearer YOUR_API_KEY'
        }
    });
    
    const result = await response.json();
    return result;
}
```

### PHP示例

```php
// 发送聊天消息
function sendMessage($message, $sessionId = null) {
    $data = [
        'message' => $message,
        'session_id' => $sessionId
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => '/api/deepseek_api.php/chat',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer YOUR_API_KEY'
        ]
    ]);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}
```

## 错误代码

| 代码 | 说明 |
|------|------|
| 400 | 请求参数错误 |
| 401 | API密钥无效 |
| 404 | 端点不存在 |
| 405 | 请求方法不允许 |
| 500 | 服务器内部错误 |
| 503 | AI服务不可用 |

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在客户端代码中暴露
2. **请求频率**: 建议控制请求频率，避免过于频繁的调用
3. **会话管理**: 使用session_id来维持对话上下文
4. **错误处理**: 请妥善处理API返回的错误信息
5. **数据备份**: 重要的对话数据建议进行备份

## 更新日志

- **v1.0.0** (2024-01-01): 初始版本发布
  - 支持基础聊天功能
  - 支持设置管理
  - 支持状态查询
