# DeepSeek AI客服API系统

## 概述

这是一个完整的DeepSeek AI客服API系统，为网站后台的AI客服功能提供RESTful API接口。系统支持聊天对话、设置管理、状态查询等功能，可以让外部应用直接调用网站后台的DeepSeek AI功能。

## 系统架构

```
网站后台AI客服系统
├── 前端界面 (xuxuemei/templates/ai_service_settings.php)
├── API接口层
│   ├── deepseek_api.php (主API接口)
│   ├── deepseek_config.php (配置管理)
│   └── api_base.php (基础API类)
├── 数据层
│   ├── ai_settings (设置表)
│   ├── ai_api_logs (日志表)
│   ├── ai_api_keys (密钥管理表)
│   └── ai_conversations (会话表)
└── 工具和文档
    ├── test_deepseek_api.html (测试工具)
    ├── example_usage.php (使用示例)
    └── 文档 (API文档)
```

## 核心功能

### 1. 聊天对话
- 支持与DeepSeek AI进行对话
- 自动管理会话上下文
- 支持自定义系统提示词
- 支持深度思考模式

### 2. 设置管理
- API密钥管理
- 模型选择
- 系统提示词配置
- 深度思考开关

### 3. 状态监控
- AI服务状态查询
- API密钥状态监控
- 使用统计信息

### 4. 安全特性
- API密钥认证
- 请求大小限制
- 频率限制
- 错误处理

## 文件说明

### 核心文件

| 文件 | 说明 |
|------|------|
| `deepseek_api.php` | 主API接口文件，处理所有API请求 |
| `deepseek_config.php` | 配置管理类，管理系统配置参数 |
| `api_base.php` | 基础API类，提供通用功能 |

### 数据库文件

| 文件 | 说明 |
|------|------|
| `sql/ai_settings_table.sql` | 数据库表结构和初始数据 |

### 工具和文档

| 文件 | 说明 |
|------|------|
| `test_deepseek_api.html` | Web界面测试工具 |
| `example_usage.php` | PHP使用示例代码 |
| `DeepSeek_API_文档.md` | 详细API文档 |
| `README.md` | 系统说明文档 |

## 快速开始

### 1. 安装数据库表

```sql
-- 执行SQL文件创建必要的数据库表
source sql/ai_settings_table.sql;
```

### 2. 配置API

编辑 `deepseek_config.php` 或设置环境变量：

```bash
export DEEPSEEK_API_TIMEOUT=30
export DEEPSEEK_MAX_TOKENS=4000
export DEEPSEEK_ENABLE_LOGGING=true
```

### 3. 设置API密钥

通过网站后台界面或API接口添加DeepSeek API密钥。

### 4. 测试API

使用测试工具验证API功能：

```bash
# 打开浏览器访问
http://your-domain.com/api/test_deepseek_api.html

# 或运行PHP示例
php example_usage.php
```

## API端点

### 基础URL
```
http://your-domain.com/api/deepseek_api.php
```

### 主要端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/chat` | POST | 发送聊天消息 |
| `/status` | GET | 获取AI状态 |
| `/models` | GET | 获取模型列表 |
| `/settings/get` | GET | 获取设置 |
| `/settings/update` | POST | 更新设置 |
| `/settings/test` | POST | 测试API连接 |

## 使用示例

### JavaScript

```javascript
// 发送聊天消息
const response = await fetch('/api/deepseek_api.php/chat', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
        message: '你好，我需要帮助',
        session_id: 'user_session_123'
    })
});

const result = await response.json();
console.log(result.data.message); // AI回复
```

### PHP

```php
// 发送聊天消息
$response = makeRequest('/api/deepseek_api.php/chat', 'POST', [
    'message' => '你好，我需要帮助',
    'session_id' => 'user_session_123'
]);

echo $response['data']['message']; // AI回复
```

### cURL

```bash
# 发送聊天消息
curl -X POST http://your-domain.com/api/deepseek_api.php/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{"message":"你好，我需要帮助","session_id":"user_session_123"}'
```

## 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DEEPSEEK_API_TIMEOUT` | 30 | API请求超时时间（秒） |
| `DEEPSEEK_MAX_TOKENS` | 4000 | 最大Token数量 |
| `DEEPSEEK_TEMPERATURE` | 0.7 | 生成温度 |
| `DEEPSEEK_MAX_CONTEXT` | 20 | 最大上下文长度 |
| `DEEPSEEK_RATE_LIMIT` | 100 | 每小时最大请求数 |
| `DEEPSEEK_ENABLE_LOGGING` | true | 是否启用日志 |

### 支持的模型

| 模型ID | 名称 | 说明 |
|--------|------|------|
| `deepseek-chat` | DeepSeek Chat | 通用对话模型 |
| `deepseek-reasoner` | DeepSeek-R1-0528 | 推理模型，支持深度思考 |

## 安全注意事项

1. **API密钥保护**: 妥善保管API密钥，不要在客户端代码中暴露
2. **访问控制**: 建议设置IP白名单或其他访问控制机制
3. **请求限制**: 合理设置请求频率限制
4. **数据加密**: 在生产环境中使用HTTPS
5. **日志监控**: 定期检查API访问日志

## 故障排除

### 常见问题

1. **API密钥无效**
   - 检查密钥格式是否正确（以sk-开头）
   - 确认密钥未过期
   - 验证密钥权限

2. **连接超时**
   - 检查网络连接
   - 调整超时设置
   - 确认DeepSeek API服务状态

3. **数据库错误**
   - 检查数据库连接
   - 确认表结构正确
   - 检查权限设置

### 调试模式

启用调试模式获取详细错误信息：

```php
// 在deepseek_config.php中设置
const LOG_LEVEL = 'DEBUG';
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础聊天功能
- 支持设置管理
- 支持状态查询
- 完整的API文档和示例

## 技术支持

如有问题或建议，请联系技术支持团队。

## 许可证

本项目采用MIT许可证。
