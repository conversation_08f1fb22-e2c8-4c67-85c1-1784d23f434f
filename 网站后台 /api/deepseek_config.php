<?php
/**
 * DeepSeek API配置文件
 * 
 * 管理API的各种配置参数
 */

class DeepSeekConfig {
    
    // API配置
    const API_VERSION = '1.0.0';
    const API_TIMEOUT = 30; // 秒
    const MAX_TOKENS = 4000;
    const DEFAULT_TEMPERATURE = 0.7;
    
    // DeepSeek API配置
    const DEEPSEEK_API_BASE = 'https://api.deepseek.com';
    const DEEPSEEK_CHAT_ENDPOINT = '/chat/completions';
    const DEEPSEEK_MODELS_ENDPOINT = '/v1/models';
    
    // 会话配置
    const MAX_CONTEXT_LENGTH = 20; // 最大上下文消息数量
    const SESSION_TIMEOUT = 3600; // 会话超时时间（秒）
    
    // 安全配置
    const MIN_API_KEY_LENGTH = 32;
    const MAX_REQUEST_SIZE = 1048576; // 1MB
    const RATE_LIMIT_REQUESTS = 100; // 每小时最大请求数
    
    // 日志配置
    const ENABLE_LOGGING = true;
    const LOG_LEVEL = 'INFO'; // DEBUG, INFO, WARNING, ERROR
    const LOG_RETENTION_DAYS = 30;
    
    // 支持的模型
    const SUPPORTED_MODELS = [
        'deepseek-chat' => [
            'name' => 'DeepSeek Chat',
            'description' => '通用对话模型，适合日常客服场景',
            'max_tokens' => 4000,
            'supports_deep_thinking' => false
        ],
        'deepseek-reasoner' => [
            'name' => 'DeepSeek-R1-0528',
            'description' => '推理模型，支持深度思考功能',
            'max_tokens' => 8000,
            'supports_deep_thinking' => true
        ]
    ];
    
    // 默认系统提示词
    const DEFAULT_SYSTEM_PROMPT = '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。';
    
    // 错误消息
    const ERROR_MESSAGES = [
        'INVALID_API_KEY' => 'API密钥无效或格式错误',
        'API_KEY_EXPIRED' => 'API密钥已过期',
        'RATE_LIMIT_EXCEEDED' => 'API请求频率过高，请稍后再试',
        'INSUFFICIENT_QUOTA' => 'API配额不足',
        'MODEL_NOT_SUPPORTED' => '不支持的模型',
        'MESSAGE_TOO_LONG' => '消息内容过长',
        'CONTEXT_TOO_LONG' => '对话上下文过长',
        'NETWORK_ERROR' => '网络连接错误',
        'SERVICE_UNAVAILABLE' => 'AI服务暂时不可用',
        'INTERNAL_ERROR' => '服务器内部错误'
    ];
    
    private static $instance = null;
    private $config = [];
    
    private function __construct() {
        $this->loadConfig();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        // 从环境变量或配置文件加载配置
        $this->config = [
            'api_version' => self::API_VERSION,
            'api_timeout' => self::API_TIMEOUT,
            'max_tokens' => self::MAX_TOKENS,
            'temperature' => self::DEFAULT_TEMPERATURE,
            'deepseek_api_base' => self::DEEPSEEK_API_BASE,
            'max_context_length' => self::MAX_CONTEXT_LENGTH,
            'session_timeout' => self::SESSION_TIMEOUT,
            'min_api_key_length' => self::MIN_API_KEY_LENGTH,
            'max_request_size' => self::MAX_REQUEST_SIZE,
            'rate_limit_requests' => self::RATE_LIMIT_REQUESTS,
            'enable_logging' => self::ENABLE_LOGGING,
            'log_level' => self::LOG_LEVEL,
            'log_retention_days' => self::LOG_RETENTION_DAYS,
            'supported_models' => self::SUPPORTED_MODELS,
            'default_system_prompt' => self::DEFAULT_SYSTEM_PROMPT,
            'error_messages' => self::ERROR_MESSAGES
        ];
        
        // 从环境变量覆盖配置
        $this->loadFromEnvironment();
    }
    
    /**
     * 从环境变量加载配置
     */
    private function loadFromEnvironment() {
        $envMappings = [
            'DEEPSEEK_API_TIMEOUT' => 'api_timeout',
            'DEEPSEEK_MAX_TOKENS' => 'max_tokens',
            'DEEPSEEK_TEMPERATURE' => 'temperature',
            'DEEPSEEK_API_BASE' => 'deepseek_api_base',
            'DEEPSEEK_MAX_CONTEXT' => 'max_context_length',
            'DEEPSEEK_SESSION_TIMEOUT' => 'session_timeout',
            'DEEPSEEK_RATE_LIMIT' => 'rate_limit_requests',
            'DEEPSEEK_ENABLE_LOGGING' => 'enable_logging',
            'DEEPSEEK_LOG_LEVEL' => 'log_level'
        ];
        
        foreach ($envMappings as $envKey => $configKey) {
            $envValue = getenv($envKey);
            if ($envValue !== false) {
                // 类型转换
                if (in_array($configKey, ['api_timeout', 'max_tokens', 'max_context_length', 'session_timeout', 'rate_limit_requests'])) {
                    $this->config[$configKey] = (int)$envValue;
                } elseif ($configKey === 'temperature') {
                    $this->config[$configKey] = (float)$envValue;
                } elseif ($configKey === 'enable_logging') {
                    $this->config[$configKey] = filter_var($envValue, FILTER_VALIDATE_BOOLEAN);
                } else {
                    $this->config[$configKey] = $envValue;
                }
            }
        }
    }
    
    /**
     * 获取配置值
     */
    public function get($key, $default = null) {
        return $this->config[$key] ?? $default;
    }
    
    /**
     * 设置配置值
     */
    public function set($key, $value) {
        $this->config[$key] = $value;
    }
    
    /**
     * 获取所有配置
     */
    public function getAll() {
        return $this->config;
    }
    
    /**
     * 验证API密钥格式
     */
    public function validateApiKeyFormat($apiKey) {
        return is_string($apiKey) 
            && strlen($apiKey) >= $this->get('min_api_key_length')
            && str_starts_with($apiKey, 'sk-');
    }
    
    /**
     * 获取支持的模型信息
     */
    public function getModelInfo($modelId) {
        $models = $this->get('supported_models');
        return $models[$modelId] ?? null;
    }
    
    /**
     * 检查模型是否支持深度思考
     */
    public function supportsDeepThinking($modelId) {
        $modelInfo = $this->getModelInfo($modelId);
        return $modelInfo['supports_deep_thinking'] ?? false;
    }
    
    /**
     * 获取错误消息
     */
    public function getErrorMessage($errorCode) {
        $messages = $this->get('error_messages');
        return $messages[$errorCode] ?? '未知错误';
    }
    
    /**
     * 获取DeepSeek API完整URL
     */
    public function getDeepSeekApiUrl($endpoint) {
        return $this->get('deepseek_api_base') . $endpoint;
    }
    
    /**
     * 验证请求大小
     */
    public function validateRequestSize($data) {
        $size = strlen(json_encode($data));
        return $size <= $this->get('max_request_size');
    }
    
    /**
     * 获取日志配置
     */
    public function getLogConfig() {
        return [
            'enabled' => $this->get('enable_logging'),
            'level' => $this->get('log_level'),
            'retention_days' => $this->get('log_retention_days')
        ];
    }
    
    /**
     * 导出配置为JSON
     */
    public function toJson() {
        return json_encode($this->config, JSON_PRETTY_PRINT);
    }
    
    /**
     * 从JSON导入配置
     */
    public function fromJson($json) {
        $data = json_decode($json, true);
        if ($data !== null) {
            $this->config = array_merge($this->config, $data);
            return true;
        }
        return false;
    }
}

// 全局配置实例
function getDeepSeekConfig() {
    return DeepSeekConfig::getInstance();
}
