<?php
/**
 * DeepSeek AI客服API接口
 * 
 * 提供对网站后台DeepSeek AI功能的API访问
 * 支持聊天对话、设置管理、状态查询等功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入基础类和配置
require_once 'api_base.php';
require_once 'deepseek_config.php';

/**
 * DeepSeek API处理类
 */
class DeepSeekAPI extends ApiBase {

    private $aiSettings;
    private $conversationContexts;
    private $config;

    public function __construct() {
        parent::__construct();
        $this->config = getDeepSeekConfig();
        $this->loadAISettings();
        $this->conversationContexts = [];
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        // 解析请求路径
        $path = $_SERVER['PATH_INFO'] ?? '';
        $path = trim($path, '/');
        
        // 分割路径获取端点和操作
        $segments = explode('/', $path);
        $endpoint = $segments[0] ?? '';
        $action = $segments[1] ?? '';
        
        // 验证API密钥
        if (!$this->validateApiKey()) {
            return $this->respondError('Invalid API key', 401);
        }
        
        // 路由处理
        switch ($endpoint) {
            case 'chat':
                return $this->handleChat();
            case 'settings':
                return $this->handleSettings($action);
            case 'status':
                return $this->handleStatus();
            case 'models':
                return $this->handleModels();
            default:
                return $this->respondError('Invalid endpoint', 404);
        }
    }
    
    /**
     * 验证API密钥
     */
    private function validateApiKey() {
        $apiKey = $this->getRequestApiKey();

        if (!$apiKey) {
            return false;
        }

        // 使用配置文件验证API密钥格式
        return $this->config->validateApiKeyFormat($apiKey);
    }
    
    /**
     * 获取请求中的API密钥
     */
    private function getRequestApiKey() {
        // 从Authorization头获取
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        // 从X-API-Key头获取
        return $_SERVER['HTTP_X_API_KEY'] ?? '';
    }
    
    /**
     * 处理聊天请求
     */
    private function handleChat() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求参数
        $message = $this->request['message'] ?? '';
        $sessionId = $this->request['session_id'] ?? uniqid();
        $customPrompt = $this->request['custom_prompt'] ?? '';
        $model = $this->request['model'] ?? $this->aiSettings['model'];
        $enableDeepThinking = $this->request['deep_thinking'] ?? $this->aiSettings['deepThinkingEnabled'];
        
        // 验证必要参数
        if (empty($message)) {
            return $this->respondError('Message is required', 400);
        }
        
        // 检查AI是否启用
        if (!$this->aiSettings['enabled']) {
            return $this->respondError('AI service is disabled', 503);
        }
        
        // 检查API密钥
        if (empty($this->aiSettings['apiKeys'])) {
            return $this->respondError('No API keys configured', 503);
        }
        
        try {
            $response = $this->callDeepSeekAPI($message, $sessionId, $customPrompt, $model, $enableDeepThinking);
            return $this->respondSuccess($response);
        } catch (Exception $e) {
            return $this->respondError('AI service error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 加载AI设置
     */
    private function loadAISettings() {
        // 从数据库或配置文件加载AI设置
        // 这里先使用默认设置，实际应该从数据库读取
        $this->aiSettings = [
            'enabled' => true,
            'apiKeys' => [], // 需要从数据库加载
            'apiKeyStatus' => [],
            'currentApiKeyIndex' => 0,
            'model' => 'deepseek-chat',
            'deepThinkingEnabled' => false,
            'replyDelay' => 0,
            'systemPrompt' => $this->config->get('default_system_prompt')
        ];
        
        // 尝试从数据库加载设置
        try {
            $this->loadSettingsFromDatabase();
        } catch (Exception $e) {
            error_log('Failed to load AI settings from database: ' . $e->getMessage());
        }
    }
    
    /**
     * 从数据库加载设置
     */
    private function loadSettingsFromDatabase() {
        if (!$this->db) {
            return;
        }
        
        try {
            // 加载AI设置
            $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM ai_settings WHERE user_id = ? OR user_id IS NULL ORDER BY user_id DESC");
            $stmt->execute([1]); // 假设管理员用户ID为1
            
            while ($row = $stmt->fetch()) {
                switch ($row['setting_key']) {
                    case 'enabled':
                        $this->aiSettings['enabled'] = (bool)$row['setting_value'];
                        break;
                    case 'api_keys':
                        $this->aiSettings['apiKeys'] = json_decode($row['setting_value'], true) ?: [];
                        break;
                    case 'api_key_status':
                        $this->aiSettings['apiKeyStatus'] = json_decode($row['setting_value'], true) ?: [];
                        break;
                    case 'model':
                        $this->aiSettings['model'] = $row['setting_value'];
                        break;
                    case 'deep_thinking_enabled':
                        $this->aiSettings['deepThinkingEnabled'] = (bool)$row['setting_value'];
                        break;
                    case 'system_prompt':
                        $this->aiSettings['systemPrompt'] = $row['setting_value'];
                        break;
                }
            }
        } catch (PDOException $e) {
            error_log('Database error loading AI settings: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取可用的API密钥
     */
    private function getAvailableApiKey() {
        if (empty($this->aiSettings['apiKeys'])) {
            return null;
        }
        
        // 查找可用的API密钥
        for ($i = 0; $i < count($this->aiSettings['apiKeys']); $i++) {
            $index = ($this->aiSettings['currentApiKeyIndex'] + $i) % count($this->aiSettings['apiKeys']);
            if (isset($this->aiSettings['apiKeyStatus'][$index]) && $this->aiSettings['apiKeyStatus'][$index]) {
                $this->aiSettings['currentApiKeyIndex'] = $index;
                return $this->aiSettings['apiKeys'][$index];
            }
        }
        
        // 如果没有标记为可用的密钥，返回第一个
        return $this->aiSettings['apiKeys'][0];
    }
    
    /**
     * 获取会话上下文
     */
    private function getConversationContext($sessionId) {
        return $this->conversationContexts[$sessionId] ?? [];
    }
    
    /**
     * 更新会话上下文
     */
    private function updateConversationContext($sessionId, $context) {
        // 限制上下文长度，使用配置中的最大长度
        $maxLength = $this->config->get('max_context_length');
        if (count($context) > $maxLength) {
            $context = array_slice($context, -$maxLength);
        }
        $this->conversationContexts[$sessionId] = $context;
    }

    /**
     * 调用DeepSeek API
     */
    private function callDeepSeekAPI($message, $sessionId, $customPrompt = '', $model = '', $enableDeepThinking = false) {
        // 获取可用的API密钥
        $apiKey = $this->getAvailableApiKey();
        if (!$apiKey) {
            throw new Exception('No available API keys');
        }

        // 准备系统提示词
        $systemPrompt = $customPrompt ?: $this->aiSettings['systemPrompt'];

        // 如果启用深度思考且模型支持
        if ($enableDeepThinking && $model === 'deepseek-reasoner') {
            $systemPrompt .= "\n\n[深度思考模式已启用] 请基于深度思考(R1)功能对问题进行深入分析和全面回答，确保回复内容详实且有深度。";
        }

        // 获取会话上下文
        $context = $this->getConversationContext($sessionId);

        // 添加当前消息到上下文
        $context[] = [
            'role' => 'user',
            'content' => $message
        ];

        // 准备请求数据
        $requestData = [
            'model' => $model ?: $this->aiSettings['model'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemPrompt
                ],
                ...$context
            ],
            'max_tokens' => $this->config->get('max_tokens'),
            'temperature' => $this->config->get('temperature'),
            'stream' => false
        ];

        // 验证请求大小
        if (!$this->config->validateRequestSize($requestData)) {
            throw new Exception($this->config->getErrorMessage('MESSAGE_TOO_LONG'));
        }

        // 发起API请求
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->config->getDeepSeekApiUrl('/chat/completions'),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($requestData),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ],
            CURLOPT_TIMEOUT => $this->config->get('api_timeout'),
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('CURL error: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception('API request failed with status: ' . $httpCode);
        }

        $responseData = json_decode($response, true);
        if (!$responseData || !isset($responseData['choices'][0]['message']['content'])) {
            throw new Exception('Invalid API response');
        }

        $aiReply = $responseData['choices'][0]['message']['content'];

        // 更新会话上下文
        $context[] = [
            'role' => 'assistant',
            'content' => $aiReply
        ];
        $this->updateConversationContext($sessionId, $context);

        return [
            'message' => $aiReply,
            'session_id' => $sessionId,
            'model' => $requestData['model'],
            'usage' => $responseData['usage'] ?? null,
            'timestamp' => time()
        ];
    }

    /**
     * 处理设置相关请求
     */
    private function handleSettings($action) {
        switch ($action) {
            case 'get':
                return $this->getSettings();
            case 'update':
                return $this->updateSettings();
            case 'test':
                return $this->testApiConnection();
            default:
                return $this->respondError('Invalid settings action', 400);
        }
    }

    /**
     * 获取AI设置
     */
    private function getSettings() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        // 隐藏敏感信息
        $settings = $this->aiSettings;
        $settings['apiKeys'] = array_map(function($key) {
            return substr($key, 0, 8) . '...' . substr($key, -8);
        }, $settings['apiKeys']);

        return $this->respondSuccess($settings);
    }

    /**
     * 更新AI设置
     */
    private function updateSettings() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        try {
            // 更新设置
            if (isset($this->request['enabled'])) {
                $this->aiSettings['enabled'] = (bool)$this->request['enabled'];
            }

            if (isset($this->request['model'])) {
                $this->aiSettings['model'] = $this->request['model'];
            }

            if (isset($this->request['deep_thinking_enabled'])) {
                $this->aiSettings['deepThinkingEnabled'] = (bool)$this->request['deep_thinking_enabled'];
            }

            if (isset($this->request['system_prompt'])) {
                $this->aiSettings['systemPrompt'] = $this->request['system_prompt'];
            }

            if (isset($this->request['api_keys'])) {
                $this->aiSettings['apiKeys'] = $this->request['api_keys'];
                // 重置API密钥状态
                $this->aiSettings['apiKeyStatus'] = array_fill(0, count($this->aiSettings['apiKeys']), false);
            }

            // 保存到数据库
            $this->saveSettingsToDatabase();

            return $this->respondSuccess(['message' => 'Settings updated successfully']);
        } catch (Exception $e) {
            return $this->respondError('Failed to update settings: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 保存设置到数据库
     */
    private function saveSettingsToDatabase() {
        if (!$this->db) {
            return;
        }

        try {
            $userId = 1; // 假设管理员用户ID为1

            $settings = [
                'enabled' => $this->aiSettings['enabled'],
                'api_keys' => json_encode($this->aiSettings['apiKeys']),
                'api_key_status' => json_encode($this->aiSettings['apiKeyStatus']),
                'model' => $this->aiSettings['model'],
                'deep_thinking_enabled' => $this->aiSettings['deepThinkingEnabled'],
                'system_prompt' => $this->aiSettings['systemPrompt']
            ];

            foreach ($settings as $key => $value) {
                $stmt = $this->db->prepare("
                    INSERT INTO ai_settings (user_id, setting_key, setting_value, updated_at)
                    VALUES (?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                ");
                $stmt->execute([$userId, $key, $value]);
            }
        } catch (PDOException $e) {
            throw new Exception('Database error: ' . $e->getMessage());
        }
    }

    /**
     * 测试API连接
     */
    private function testApiConnection() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        $apiKey = $this->request['api_key'] ?? '';
        if (empty($apiKey)) {
            return $this->respondError('API key is required', 400);
        }

        try {
            $result = $this->validateDeepSeekApiKey($apiKey);
            return $this->respondSuccess($result);
        } catch (Exception $e) {
            return $this->respondError('Connection test failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 验证DeepSeek API密钥
     */
    private function validateDeepSeekApiKey($apiKey) {
        if (!$apiKey || !str_starts_with($apiKey, 'sk-')) {
            return [
                'valid' => false,
                'error' => 'API密钥格式不正确，应以sk-开头'
            ];
        }

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'https://api.deepseek.com/v1/models',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $apiKey,
                'Content-Type: application/json'
            ],
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'valid' => false,
                'error' => '网络连接错误: ' . $error
            ];
        }

        if ($httpCode === 200) {
            $data = json_decode($response, true);
            return [
                'valid' => true,
                'models' => $data['data'] ?? [],
                'message' => 'API密钥验证成功'
            ];
        } elseif ($httpCode === 401) {
            return [
                'valid' => false,
                'error' => 'API密钥无效或已过期'
            ];
        } elseif ($httpCode === 429) {
            return [
                'valid' => false,
                'error' => 'API请求频率过高，请稍后再试'
            ];
        } else {
            return [
                'valid' => false,
                'error' => "API验证失败 (HTTP $httpCode)"
            ];
        }
    }

    /**
     * 处理状态查询
     */
    private function handleStatus() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        $availableKeys = 0;
        foreach ($this->aiSettings['apiKeyStatus'] as $status) {
            if ($status) $availableKeys++;
        }

        return $this->respondSuccess([
            'enabled' => $this->aiSettings['enabled'],
            'total_api_keys' => count($this->aiSettings['apiKeys']),
            'available_api_keys' => $availableKeys,
            'current_model' => $this->aiSettings['model'],
            'deep_thinking_enabled' => $this->aiSettings['deepThinkingEnabled'],
            'status' => $this->aiSettings['enabled'] && $availableKeys > 0 ? 'active' : 'inactive'
        ]);
    }

    /**
     * 处理模型列表查询
     */
    private function handleModels() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        $supportedModels = $this->config->get('supported_models');
        $models = [];

        foreach ($supportedModels as $id => $info) {
            $models[] = [
                'id' => $id,
                'name' => $info['name'],
                'description' => $info['description'],
                'max_tokens' => $info['max_tokens'],
                'supports_deep_thinking' => $info['supports_deep_thinking']
            ];
        }

        return $this->respondSuccess([
            'models' => $models,
            'current_model' => $this->aiSettings['model']
        ]);
    }
}

// 创建API实例并处理请求
$api = new DeepSeekAPI();
$api->handleRequest();
