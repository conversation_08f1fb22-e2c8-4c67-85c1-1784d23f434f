<?php
/**
 * DeepSeek API集成测试脚本
 * 全面测试网站后台DeepSeek API接口功能
 */

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API集成测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .test-section { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 4px solid #007bff; }
        .test-item { background: white; border-radius: 8px; padding: 15px; margin: 15px 0; border: 1px solid #e9ecef; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-test { background: #28a745; }
        .btn-test:hover { background: #1e7e34; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #e9ecef; max-height: 300px; overflow-y: auto; }
        .progress { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; }
        .progress-bar { background: #28a745; height: 100%; border-radius: 10px; transition: width 0.3s; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .api-endpoint { background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .loading { display: none; text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 DeepSeek API集成测试</h1>
        
        <div class="test-section">
            <h2>📋 测试概览</h2>
            <p>本测试将全面检查DeepSeek API接口的各项功能，包括：</p>
            <ul style="margin: 15px 0; padding-left: 30px;">
                <li>数据库连接和表结构</li>
                <li>API接口可访问性</li>
                <li>配置文件完整性</li>
                <li>前端界面功能</li>
                <li>API端点响应</li>
                <li>错误处理机制</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔍 第一步：基础环境检查</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>数据库连接</h3>
                    <?php
                    try {
                        require_once 'includes/db.php';
                        if ($pdo) {
                            echo "<div class='status success'>✅ 数据库连接成功</div>";
                            
                            // 检查数据库信息
                            $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as version");
                            $db_info = $stmt->fetch();
                            echo "<div class='info'>📊 数据库: {$db_info['db_name']}<br>版本: {$db_info['version']}</div>";
                        } else {
                            echo "<div class='status error'>❌ 数据库连接失败</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='status error'>❌ 数据库错误: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>

                <div class="test-item">
                    <h3>AI相关表检查</h3>
                    <?php
                    if (isset($pdo) && $pdo) {
                        $ai_tables = ['ai_settings', 'ai_api_logs', 'ai_api_keys', 'ai_conversations'];
                        $existing_tables = [];
                        
                        foreach ($ai_tables as $table) {
                            try {
                                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                                if ($stmt->rowCount() > 0) {
                                    $existing_tables[] = $table;
                                    echo "<div class='status success'>✅ $table</div>";
                                } else {
                                    echo "<div class='status error'>❌ $table 不存在</div>";
                                }
                            } catch (Exception $e) {
                                echo "<div class='status error'>❌ $table 检查失败</div>";
                            }
                        }
                        
                        $table_progress = (count($existing_tables) / count($ai_tables)) * 100;
                        echo "<div class='progress'><div class='progress-bar' style='width: {$table_progress}%'></div></div>";
                        echo "<p>表完整性: " . count($existing_tables) . "/" . count($ai_tables) . "</p>";
                        
                        if (count($existing_tables) < count($ai_tables)) {
                            echo "<div class='warning'>⚠️ 部分表缺失，请运行 <a href='fix_ai_database.php'>数据库修复脚本</a></div>";
                        }
                    }
                    ?>
                </div>

                <div class="test-item">
                    <h3>API文件检查</h3>
                    <?php
                    $api_files = [
                        'api/deepseek_api.php' => 'DeepSeek API主文件',
                        'api/deepseek_config.php' => '配置管理文件',
                        'api/api_base.php' => 'API基础类',
                        'xuxuemei/templates/ai_service_settings.php' => 'AI设置界面'
                    ];
                    
                    $existing_files = 0;
                    foreach ($api_files as $file => $description) {
                        if (file_exists($file)) {
                            echo "<div class='status success'>✅ $description</div>";
                            $existing_files++;
                        } else {
                            echo "<div class='status error'>❌ $description 缺失</div>";
                        }
                    }
                    
                    $file_progress = ($existing_files / count($api_files)) * 100;
                    echo "<div class='progress'><div class='progress-bar' style='width: {$file_progress}%'></div></div>";
                    echo "<p>文件完整性: $existing_files/" . count($api_files) . "</p>";
                    ?>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 第二步：API接口测试</h2>
            <div class="test-item">
                <h3>API端点可访问性测试</h3>
                <div id="api-test-results">
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>正在测试API接口...</p>
                    </div>
                </div>
                <button onclick="testApiEndpoints()" class="btn btn-test">🧪 开始API测试</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 第三步：功能集成测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>前端界面测试</h3>
                    <div class="api-endpoint">
                        <strong>AI客服设置页面:</strong><br>
                        <a href="xuxuemei/index.php?page=ai_service_settings" target="_blank">
                            /xuxuemei/index.php?page=ai_service_settings
                        </a>
                    </div>
                    <button onclick="testFrontendInterface()" class="btn">🖥️ 测试前端界面</button>
                    <div id="frontend-test-result"></div>
                </div>

                <div class="test-item">
                    <h3>API测试工具</h3>
                    <div class="api-endpoint">
                        <strong>API测试页面:</strong><br>
                        <a href="api/test_deepseek_api.html" target="_blank">
                            /api/test_deepseek_api.html
                        </a>
                    </div>
                    <button onclick="testApiTool()" class="btn">🔧 测试API工具</button>
                    <div id="api-tool-test-result"></div>
                </div>

                <div class="test-item">
                    <h3>配置管理测试</h3>
                    <?php
                    try {
                        if (file_exists('api/deepseek_config.php')) {
                            require_once 'api/deepseek_config.php';
                            $config = getDeepSeekConfig();
                            echo "<div class='status success'>✅ 配置类加载成功</div>";
                            echo "<div class='info'>📋 支持的模型数量: " . count($config->get('supported_models')) . "</div>";
                            echo "<div class='info'>⏱️ API超时设置: " . $config->get('api_timeout') . "秒</div>";
                        } else {
                            echo "<div class='status error'>❌ 配置文件不存在</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='status error'>❌ 配置加载失败: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 测试总结</h2>
            <div id="test-summary">
                <p>请完成上述所有测试项目以获取完整的测试报告。</p>
            </div>
        </div>
    </div>

    <script>
        // API端点测试
        async function testApiEndpoints() {
            const loading = document.getElementById('loading');
            const results = document.getElementById('api-test-results');
            
            loading.style.display = 'block';
            results.innerHTML = '<div class="loading" id="loading"><div class="spinner"></div><p>正在测试API接口...</p></div>';
            
            const endpoints = [
                { path: '/api/deepseek_api.php/status', method: 'GET', name: '状态查询' },
                { path: '/api/deepseek_api.php/models', method: 'GET', name: '模型列表' },
                { path: '/api/deepseek_api.php/settings/get', method: 'GET', name: '获取设置' }
            ];
            
            let testResults = '<h4>API端点测试结果:</h4>';
            let successCount = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.path, {
                        method: endpoint.method,
                        headers: {
                            'Authorization': 'Bearer test-key-for-endpoint-testing'
                        }
                    });
                    
                    if (response.ok || response.status === 401) {
                        testResults += `<div class="status success">✅ ${endpoint.name} - 端点可访问</div>`;
                        successCount++;
                    } else {
                        testResults += `<div class="status error">❌ ${endpoint.name} - HTTP ${response.status}</div>`;
                    }
                } catch (error) {
                    testResults += `<div class="status error">❌ ${endpoint.name} - 连接失败: ${error.message}</div>`;
                }
            }
            
            const progress = (successCount / endpoints.length) * 100;
            testResults += `<div class="progress"><div class="progress-bar" style="width: ${progress}%"></div></div>`;
            testResults += `<p>API可访问性: ${successCount}/${endpoints.length}</p>`;
            
            results.innerHTML = testResults;
        }
        
        // 前端界面测试
        function testFrontendInterface() {
            const result = document.getElementById('frontend-test-result');
            
            // 尝试在新窗口打开前端界面
            const testWindow = window.open('xuxuemei/index.php?page=ai_service_settings', '_blank');
            
            if (testWindow) {
                result.innerHTML = '<div class="status success">✅ 前端界面已在新窗口打开，请检查是否正常显示</div>';
            } else {
                result.innerHTML = '<div class="status error">❌ 无法打开前端界面，可能被浏览器阻止</div>';
            }
        }
        
        // API工具测试
        function testApiTool() {
            const result = document.getElementById('api-tool-test-result');
            
            // 尝试在新窗口打开API测试工具
            const testWindow = window.open('api/test_deepseek_api.html', '_blank');
            
            if (testWindow) {
                result.innerHTML = '<div class="status success">✅ API测试工具已在新窗口打开</div>';
            } else {
                result.innerHTML = '<div class="status error">❌ 无法打开API测试工具</div>';
            }
        }
        
        // 页面加载完成后自动开始基础测试
        window.onload = function() {
            console.log('DeepSeek API集成测试页面已加载');
        };
    </script>
</body>
</html>
