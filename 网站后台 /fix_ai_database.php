<?php
/**
 * AI数据库修复脚本
 * 解决导入问题，直接在现有数据库上创建表
 */

// 引入数据库连接
require_once 'includes/db.php';

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI数据库修复工具</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .status { padding: 15px; border-radius: 8px; margin: 15px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .step { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 15px 0; }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #e9ecef; }
        .table-info { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .progress { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; }
        .progress-bar { background: #007bff; height: 100%; border-radius: 10px; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ AI数据库修复工具</h1>
        
        <?php
        // 检查数据库连接
        if (!$pdo) {
            echo "<div class='status error'>❌ 数据库连接失败！请检查数据库配置。</div>";
            echo "</div></body></html>";
            exit;
        }
        
        echo "<div class='status success'>✅ 数据库连接成功</div>";
        
        // 检查当前数据库信息
        try {
            $stmt = $pdo->query("SELECT DATABASE() as db_name");
            $db_info = $stmt->fetch();
            echo "<div class='info'>📊 当前数据库: <strong>{$db_info['db_name']}</strong></div>";
        } catch (Exception $e) {
            echo "<div class='status error'>❌ 获取数据库信息失败: " . $e->getMessage() . "</div>";
        }
        
        // 检查现有的AI相关表
        echo "<div class='step'>";
        echo "<h3>🔍 检查现有表结构</h3>";
        
        $ai_tables = ['ai_settings', 'ai_api_logs', 'ai_api_keys', 'ai_conversations'];
        $existing_tables = [];
        
        foreach ($ai_tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $existing_tables[] = $table;
                    echo "<div class='status success'>✅ 表 '$table' 已存在</div>";
                    
                    // 显示表结构信息
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
                    $count = $stmt->fetch()['count'];
                    echo "<div class='table-info'>📋 表 '$table' 包含 $count 条记录</div>";
                } else {
                    echo "<div class='status warning'>⚠️ 表 '$table' 不存在</div>";
                }
            } catch (Exception $e) {
                echo "<div class='status error'>❌ 检查表 '$table' 失败: " . $e->getMessage() . "</div>";
            }
        }
        echo "</div>";
        
        // 如果表不完整，提供修复选项
        if (count($existing_tables) < count($ai_tables)) {
            echo "<div class='step'>";
            echo "<h3>🔧 修复缺失的表</h3>";
            echo "<p>检测到缺失的表，点击下面的按钮进行修复：</p>";
            
            if (isset($_POST['fix_tables'])) {
                // 执行修复
                $sql_commands = [
                    'ai_settings' => "CREATE TABLE IF NOT EXISTS `ai_settings` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
                        `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
                        `setting_value` text COMMENT '设置值',
                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `idx_user_id` (`user_id`),
                        KEY `idx_setting_key` (`setting_key`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI客服设置表'",
                    
                    'ai_api_logs' => "CREATE TABLE IF NOT EXISTS `ai_api_logs` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
                        `user_message` text COMMENT '用户消息',
                        `ai_response` text COMMENT 'AI回复',
                        `model_used` varchar(50) DEFAULT NULL COMMENT '使用的模型',
                        `api_key_used` varchar(20) DEFAULT NULL COMMENT '使用的API密钥',
                        `response_time` int(11) DEFAULT NULL COMMENT '响应时间',
                        `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
                        `success` tinyint(1) DEFAULT 1 COMMENT '是否成功',
                        `error_message` text COMMENT '错误信息',
                        `ip_address` varchar(45) DEFAULT NULL COMMENT '请求IP',
                        `user_agent` text COMMENT '用户代理',
                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `idx_session_id` (`session_id`),
                        KEY `idx_created_at` (`created_at`),
                        KEY `idx_success` (`success`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API调用日志表'",
                    
                    'ai_api_keys' => "CREATE TABLE IF NOT EXISTS `ai_api_keys` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `key_name` varchar(100) DEFAULT NULL COMMENT '密钥名称',
                        `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
                        `key_hash` varchar(64) NOT NULL COMMENT '密钥哈希值',
                        `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
                        `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
                        `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
                        `error_count` int(11) DEFAULT 0 COMMENT '错误次数',
                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `idx_is_active` (`is_active`),
                        KEY `idx_last_used` (`last_used_at`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API密钥管理表'",
                    
                    'ai_conversations' => "CREATE TABLE IF NOT EXISTS `ai_conversations` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `session_id` varchar(100) NOT NULL COMMENT '会话ID',
                        `message_index` int(11) NOT NULL COMMENT '消息索引',
                        `role` enum('user','assistant','system') NOT NULL COMMENT '角色',
                        `content` text NOT NULL COMMENT '消息内容',
                        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `idx_session_id` (`session_id`),
                        KEY `idx_created_at` (`created_at`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI会话上下文表'"
                ];
                
                $success_count = 0;
                $total_count = count($sql_commands);
                
                foreach ($sql_commands as $table_name => $sql) {
                    try {
                        $pdo->exec($sql);
                        echo "<div class='status success'>✅ 表 '$table_name' 创建成功</div>";
                        $success_count++;
                    } catch (Exception $e) {
                        echo "<div class='status error'>❌ 创建表 '$table_name' 失败: " . $e->getMessage() . "</div>";
                    }
                }
                
                // 插入默认设置
                if ($success_count > 0) {
                    try {
                        $default_settings = [
                            ['enabled', '1'],
                            ['model', 'deepseek-chat'],
                            ['deep_thinking_enabled', '0'],
                            ['system_prompt', '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'],
                            ['api_keys', '[]'],
                            ['api_key_status', '[]']
                        ];
                        
                        foreach ($default_settings as $setting) {
                            $stmt = $pdo->prepare("INSERT IGNORE INTO `ai_settings` (`user_id`, `setting_key`, `setting_value`) VALUES (NULL, ?, ?)");
                            $stmt->execute($setting);
                        }
                        
                        echo "<div class='status success'>✅ 默认设置插入成功</div>";
                    } catch (Exception $e) {
                        echo "<div class='status warning'>⚠️ 插入默认设置时出现问题: " . $e->getMessage() . "</div>";
                    }
                }
                
                echo "<div class='progress'>";
                $progress = ($success_count / $total_count) * 100;
                echo "<div class='progress-bar' style='width: {$progress}%'></div>";
                echo "</div>";
                echo "<p>修复进度: $success_count / $total_count 个表</p>";
                
                if ($success_count === $total_count) {
                    echo "<div class='status success'>🎉 所有表修复完成！</div>";
                    echo "<a href='xuxuemei/index.php?page=ai_service_settings' class='btn btn-success'>前往AI客服设置</a>";
                }
            } else {
                echo "<form method='post'>";
                echo "<button type='submit' name='fix_tables' class='btn'>🔧 开始修复表结构</button>";
                echo "</form>";
            }
            echo "</div>";
        } else {
            echo "<div class='status success'>🎉 所有AI相关表都已存在！</div>";
            echo "<div class='step'>";
            echo "<h3>🚀 下一步操作</h3>";
            echo "<ul>";
            echo "<li><a href='xuxuemei/index.php?page=ai_service_settings' class='btn'>前往AI客服设置</a></li>";
            echo "<li><a href='api/test_deepseek_api.html' class='btn'>测试API接口</a></li>";
            echo "<li><a href='api/DeepSeek_API_文档.md' class='btn'>查看API文档</a></li>";
            echo "</ul>";
            echo "</div>";
        }
        
        // 显示表结构详情
        if (count($existing_tables) > 0) {
            echo "<div class='step'>";
            echo "<h3>📋 表结构详情</h3>";
            
            foreach ($existing_tables as $table) {
                try {
                    echo "<h4>表: $table</h4>";
                    $stmt = $pdo->query("DESCRIBE `$table`");
                    $columns = $stmt->fetchAll();
                    
                    echo "<pre>";
                    foreach ($columns as $column) {
                        echo sprintf("%-20s %-20s %-10s %-10s\n", 
                            $column['Field'], 
                            $column['Type'], 
                            $column['Null'], 
                            $column['Key']
                        );
                    }
                    echo "</pre>";
                } catch (Exception $e) {
                    echo "<div class='status error'>❌ 获取表 '$table' 结构失败: " . $e->getMessage() . "</div>";
                }
            }
            echo "</div>";
        }
        ?>
        
        <div class="step">
            <h3>📚 相关文件</h3>
            <ul>
                <li><strong>API接口:</strong> <code>/api/deepseek_api.php</code></li>
                <li><strong>配置文件:</strong> <code>/api/deepseek_config.php</code></li>
                <li><strong>测试工具:</strong> <code>/api/test_deepseek_api.html</code></li>
                <li><strong>使用示例:</strong> <code>/api/example_usage.php</code></li>
                <li><strong>文档:</strong> <code>/api/README.md</code></li>
            </ul>
        </div>
        
        <div class="info">
            <p><strong>提示:</strong> 如果仍然遇到问题，请检查数据库用户权限，确保有CREATE、ALTER、INSERT等权限。</p>
        </div>
    </div>
</body>
</html>
