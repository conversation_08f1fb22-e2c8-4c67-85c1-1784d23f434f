<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek 简化API测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 900px; margin: 0 auto; background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .test-section { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 4px solid #007bff; }
        .test-item { background: white; border-radius: 8px; padding: 15px; margin: 15px 0; border: 1px solid #e9ecef; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-test { background: #28a745; }
        .btn-test:hover { background: #1e7e34; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #e9ecef; max-height: 300px; overflow-y: auto; }
        .loading { display: none; text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .api-endpoint { background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0; font-family: monospace; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 DeepSeek 简化API测试</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>这是DeepSeek API的简化版本测试，不依赖复杂的基础类，直接测试核心功能。</p>
            <p><strong>API地址:</strong> <code>/api/deepseek_simple.php</code></p>
        </div>

        <div class="test-section">
            <h2>🔍 API端点测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>状态查询</h3>
                    <div class="api-endpoint">GET /api/deepseek_simple.php/status</div>
                    <button onclick="testStatus()" class="btn btn-test">测试状态</button>
                    <div id="status-result"></div>
                </div>

                <div class="test-item">
                    <h3>模型列表</h3>
                    <div class="api-endpoint">GET /api/deepseek_simple.php/models</div>
                    <button onclick="testModels()" class="btn btn-test">测试模型</button>
                    <div id="models-result"></div>
                </div>

                <div class="test-item">
                    <h3>获取设置</h3>
                    <div class="api-endpoint">GET /api/deepseek_simple.php/settings/get</div>
                    <button onclick="testSettings()" class="btn btn-test">测试设置</button>
                    <div id="settings-result"></div>
                </div>

                <div class="test-item">
                    <h3>聊天对话</h3>
                    <div class="api-endpoint">POST /api/deepseek_simple.php/chat</div>
                    <input type="text" id="chat-message" placeholder="输入测试消息" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
                    <button onclick="testChat()" class="btn btn-test">测试聊天</button>
                    <div id="chat-result"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 一键测试所有功能</h2>
            <button onclick="testAll()" class="btn" style="background: #17a2b8;">🧪 运行所有测试</button>
            <div id="all-results"></div>
        </div>

        <div class="test-section">
            <h2>🔗 相关链接</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h4>🛠️ 数据库修复</h4>
                    <a href="fix_ai_database.php" target="_blank" class="btn">修复数据库</a>
                </div>
                <div class="test-item">
                    <h4>⚙️ AI设置界面</h4>
                    <a href="xuxuemei/index.php?page=ai_service_settings" target="_blank" class="btn">AI设置</a>
                </div>
                <div class="test-item">
                    <h4>📊 完整测试</h4>
                    <a href="test_api_status.html" target="_blank" class="btn">完整测试</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 通用API请求函数
        async function makeApiRequest(endpoint, method = 'GET', data = null) {
            const url = `/api/deepseek_simple.php${endpoint}`;
            
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer test-api-key-for-testing'
                }
            };
            
            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    success: false,
                    status: 0,
                    data: { error: error.message }
                };
            }
        }
        
        // 显示结果
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            
            if (result.success) {
                element.innerHTML = `
                    <div class="status success">✅ 请求成功 (${result.status})</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.innerHTML = `
                    <div class="status error">❌ 请求失败 (${result.status})</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            }
        }
        
        // 测试状态
        async function testStatus() {
            const result = await makeApiRequest('/status');
            displayResult('status-result', result);
        }
        
        // 测试模型
        async function testModels() {
            const result = await makeApiRequest('/models');
            displayResult('models-result', result);
        }
        
        // 测试设置
        async function testSettings() {
            const result = await makeApiRequest('/settings/get');
            displayResult('settings-result', result);
        }
        
        // 测试聊天
        async function testChat() {
            const message = document.getElementById('chat-message').value || '你好，这是一个测试消息';
            const result = await makeApiRequest('/chat', 'POST', {
                message: message,
                session_id: 'test_session_' + Date.now()
            });
            displayResult('chat-result', result);
        }
        
        // 测试所有功能
        async function testAll() {
            const resultsDiv = document.getElementById('all-results');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>正在运行所有测试...</p></div>';
            
            const tests = [
                { name: '状态查询', func: () => makeApiRequest('/status') },
                { name: '模型列表', func: () => makeApiRequest('/models') },
                { name: '获取设置', func: () => makeApiRequest('/settings/get') },
                { name: '聊天对话', func: () => makeApiRequest('/chat', 'POST', { message: '测试消息', session_id: 'test_' + Date.now() }) }
            ];
            
            let html = '<h3>📊 测试结果汇总</h3>';
            let passCount = 0;
            
            for (const test of tests) {
                const result = await test.func();
                
                if (result.success) {
                    html += `<div class="status success">✅ ${test.name} - 通过</div>`;
                    passCount++;
                } else {
                    html += `<div class="status error">❌ ${test.name} - 失败: ${result.data.error || '未知错误'}</div>`;
                }
            }
            
            html += `<div class="info">📈 测试通过率: ${passCount}/${tests.length} (${Math.round(passCount/tests.length*100)}%)</div>`;
            
            if (passCount === tests.length) {
                html += '<div class="status success">🎉 所有测试都通过了！API功能正常。</div>';
            } else {
                html += '<div class="status error">⚠️ 部分测试失败，请检查API配置和数据库连接。</div>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('DeepSeek 简化API测试页面已加载');
        };
    </script>
</body>
</html>
