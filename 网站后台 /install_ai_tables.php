<?php
/**
 * AI设置表安装脚本
 * 直接在现有数据库上创建AI相关表
 */

// 引入数据库连接
require_once 'includes/db.php';

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>AI设置表安装</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .step { margin: 15px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🤖 AI设置表安装程序</h1>
        <p>正在为DeepSeek API功能创建必要的数据库表...</p>";

// 检查数据库连接
if (!$pdo) {
    echo "<div class='error'>❌ 数据库连接失败！请检查数据库配置。</div>";
    echo "</div></body></html>";
    exit;
}

echo "<div class='success'>✅ 数据库连接成功</div>";

// 定义要执行的SQL语句
$sql_statements = [
    'ai_settings' => [
        'description' => '创建AI设置表',
        'sql' => "CREATE TABLE IF NOT EXISTS `ai_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL COMMENT '用户ID，NULL表示全局设置',
            `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
            `setting_value` text COMMENT '设置值',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI客服设置表'"
    ],
    'ai_settings_unique' => [
        'description' => '添加AI设置表唯一约束',
        'sql' => "ALTER TABLE `ai_settings` ADD UNIQUE KEY `unique_user_setting` (`user_id`, `setting_key`)",
        'ignore_error' => true
    ],
    'ai_settings_data' => [
        'description' => '插入默认AI设置',
        'sql' => "INSERT IGNORE INTO `ai_settings` (`user_id`, `setting_key`, `setting_value`) VALUES
            (NULL, 'enabled', '1'),
            (NULL, 'model', 'deepseek-chat'),
            (NULL, 'deep_thinking_enabled', '0'),
            (NULL, 'system_prompt', '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'),
            (NULL, 'api_keys', '[]'),
            (NULL, 'api_key_status', '[]')"
    ],
    'ai_api_logs' => [
        'description' => '创建API访问日志表',
        'sql' => "CREATE TABLE IF NOT EXISTS `ai_api_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
            `user_message` text COMMENT '用户消息',
            `ai_response` text COMMENT 'AI回复',
            `model_used` varchar(50) DEFAULT NULL COMMENT '使用的模型',
            `api_key_used` varchar(20) DEFAULT NULL COMMENT '使用的API密钥（脱敏）',
            `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
            `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
            `success` tinyint(1) DEFAULT 1 COMMENT '是否成功',
            `error_message` text COMMENT '错误信息',
            `ip_address` varchar(45) DEFAULT NULL COMMENT '请求IP',
            `user_agent` text COMMENT '用户代理',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_session_id` (`session_id`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_success` (`success`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API调用日志表'"
    ],
    'ai_api_keys' => [
        'description' => '创建API密钥管理表',
        'sql' => "CREATE TABLE IF NOT EXISTS `ai_api_keys` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `key_name` varchar(100) DEFAULT NULL COMMENT '密钥名称',
            `api_key` varchar(255) NOT NULL COMMENT 'API密钥（加密存储）',
            `key_hash` varchar(64) NOT NULL COMMENT '密钥哈希值',
            `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
            `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
            `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
            `error_count` int(11) DEFAULT 0 COMMENT '错误次数',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_is_active` (`is_active`),
            KEY `idx_last_used` (`last_used_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API密钥管理表'"
    ],
    'ai_api_keys_unique' => [
        'description' => '添加API密钥表唯一约束',
        'sql' => "ALTER TABLE `ai_api_keys` ADD UNIQUE KEY `unique_key_hash` (`key_hash`)",
        'ignore_error' => true
    ],
    'ai_conversations' => [
        'description' => '创建会话上下文表',
        'sql' => "CREATE TABLE IF NOT EXISTS `ai_conversations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `session_id` varchar(100) NOT NULL COMMENT '会话ID',
            `message_index` int(11) NOT NULL COMMENT '消息索引',
            `role` enum('user','assistant','system') NOT NULL COMMENT '角色',
            `content` text NOT NULL COMMENT '消息内容',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_session_id` (`session_id`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI会话上下文表'"
    ],
    'ai_conversations_unique' => [
        'description' => '添加会话表唯一约束',
        'sql' => "ALTER TABLE `ai_conversations` ADD UNIQUE KEY `unique_session_message` (`session_id`, `message_index`)",
        'ignore_error' => true
    ]
];

$success_count = 0;
$error_count = 0;

// 执行SQL语句
foreach ($sql_statements as $key => $statement) {
    echo "<div class='step'>";
    echo "<h3>📋 {$statement['description']}</h3>";
    
    try {
        $pdo->exec($statement['sql']);
        echo "<div class='success'>✅ 执行成功</div>";
        $success_count++;
    } catch (PDOException $e) {
        if (isset($statement['ignore_error']) && $statement['ignore_error']) {
            echo "<div class='info'>ℹ️ 跳过（可能已存在）: " . $e->getMessage() . "</div>";
        } else {
            echo "<div class='error'>❌ 执行失败: " . $e->getMessage() . "</div>";
            $error_count++;
        }
    }
    
    echo "</div>";
}

// 验证表是否创建成功
echo "<div class='step'>";
echo "<h3>🔍 验证表结构</h3>";

$tables_to_check = ['ai_settings', 'ai_api_logs', 'ai_api_keys', 'ai_conversations'];
$existing_tables = [];

foreach ($tables_to_check as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[] = $table;
            echo "<div class='success'>✅ 表 '$table' 存在</div>";
        } else {
            echo "<div class='error'>❌ 表 '$table' 不存在</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='error'>❌ 检查表 '$table' 时出错: " . $e->getMessage() . "</div>";
    }
}

echo "</div>";

// 显示总结
echo "<div class='step'>";
echo "<h3>📊 安装总结</h3>";
echo "<p><strong>成功操作:</strong> $success_count</p>";
echo "<p><strong>错误操作:</strong> $error_count</p>";
echo "<p><strong>已创建表:</strong> " . count($existing_tables) . " / " . count($tables_to_check) . "</p>";

if (count($existing_tables) === count($tables_to_check)) {
    echo "<div class='success'>🎉 所有AI相关表已成功创建！现在可以使用DeepSeek API功能了。</div>";
} else {
    echo "<div class='error'>⚠️ 部分表创建失败，请检查错误信息并手动处理。</div>";
}

echo "</div>";

// 显示下一步操作
echo "<div class='step'>";
echo "<h3>🚀 下一步操作</h3>";
echo "<ol>";
echo "<li>访问网站后台的 <strong>AI客服设置</strong> 页面</li>";
echo "<li>添加您的DeepSeek API密钥</li>";
echo "<li>配置AI模型和系统提示词</li>";
echo "<li>测试API连接</li>";
echo "<li>开始使用AI客服功能</li>";
echo "</ol>";
echo "</div>";

echo "<div class='info'>";
echo "<h4>📝 API接口地址</h4>";
echo "<p>DeepSeek API接口: <code>/api/deepseek_api.php</code></p>";
echo "<p>测试工具: <code>/api/test_deepseek_api.html</code></p>";
echo "<p>使用文档: <code>/api/DeepSeek_API_文档.md</code></p>";
echo "</div>";

echo "</div></body></html>";
?>
